/* Contact.css */
.contact-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.contact-container h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.contact-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
  position: relative;
}

.contact-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
}

.contact-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid #ecf0f1;
}

.contact-header h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 2rem;
}

.contact-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 15px;
  transition: all 0.3s ease;
  border-left: 5px solid #3498db;
}

.contact-item:hover {
  background: #e9ecef;
  transform: translateX(10px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.contact-icon {
  font-size: 2rem;
  margin-right: 1.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.contact-details {
  flex: 1;
}

.contact-details h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1.3rem;
}

.contact-details p {
  color: #34495e;
  margin: 0;
  font-size: 1.1rem;
}

.contact-details a {
  color: #3498db;
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 500;
}

.contact-details a:hover {
  color: #2980b9;
  text-decoration: underline;
}

.contact-footer {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 2px solid #ecf0f1;
}

.social-links h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.social-links p {
  color: #7f8c8d;
  font-style: italic;
  margin: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .contact-container {
    padding: 15px;
  }
  
  .contact-container h1 {
    font-size: 2rem;
  }
  
  .contact-card {
    padding: 1.5rem;
  }
  
  .contact-header h2 {
    font-size: 1.5rem;
  }
  
  .contact-item {
    flex-direction: column;
    text-align: center;
    padding: 1rem;
  }
  
  .contact-icon {
    margin-right: 0;
    margin-bottom: 1rem;
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
  
  .contact-details h3 {
    font-size: 1.1rem;
  }
  
  .contact-details p {
    font-size: 1rem;
  }
}
