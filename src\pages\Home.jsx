import { useNavigate } from 'react-router-dom';
import './Home.css';

function Home() {
  const navigate = useNavigate();

  const handleNavigateToAbout = () => {
    navigate('/about');
  };

  return (
    <div className="home-container">
      <h1>Trang Chủ - React Router v6</h1>
      
      <div className="theory-section">
        <h2>Nội dung lý thuyết trình bày</h2>
        
        <div className="theory-item">
          <h3>🔄 Khác biệt giữa React Router v5 và v6</h3>
          <div className="comparison">
            <div className="version-block">
              <h4>React Router v5:</h4>
              <ul>
                <li>Sử dụng <code>Switch</code> component</li>
                <li>Sử dụng <code>useHistory</code> hook</li>
                <li>Route có thể được đặt ở bất kỳ đâu</li>
                <li><PERSON><PERSON> pháp: <code>Route path="/about" component=About /</code></li>
                <li>Nested routes phức tạp hơn</li>
              </ul>
            </div>
            <div className="version-block">
              <h4>React Router v6:</h4>
              <ul>
                <li>Sử dụng <code>Routes</code> component (thay thế Switch)</li>
                <li>Sử dụng <code>useNavigate</code> hook (thay thế useHistory)</li>
                <li>Tất cả routes phải được đặt trong <code>Routes</code></li>
                <li>Cú pháp: <code>Route path="/about" element=About /</code></li>
                <li>Nested routes đơn giản hơn với relative paths</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="theory-item">
          <h3>🗂️ Nested Routes, Route Params, Redirect</h3>
          <div className="concept-explanation">
            <h4>Nested Routes:</h4>
            <p>Cho phép tạo các route con bên trong route cha. Trong v6, sử dụng <code>Outlet</code> component để render route con.</p>
            <div className="code-example">
              <p><strong>Ví dụ Nested Routes:</strong></p>
              <p><code>Route path="/dashboard" element=Dashboard</code></p>
              <p><code>  Route path="profile" element=Profile /</code></p>
              <p><code>  Route path="settings" element=Settings /</code></p>
              <p><code>/Route</code></p>
            </div>

            <h4>Route Params:</h4>
            <p>Sử dụng <code>useParams</code> hook để lấy tham số từ URL.</p>
            <div className="code-example">
              <p><strong>Ví dụ Route Params:</strong></p>
              <p><code>Route path="/user/:id" element=User /</code></p>
              <p><code>// Trong component User:</code></p>
              <p><code>const id = useParams();</code></p>
            </div>

            <h4>Redirect:</h4>
            <p>Trong v6, sử dụng <code>Navigate</code> component thay vì <code>Redirect</code>.</p>
            <div className="code-example">
              <p><strong>Ví dụ Redirect:</strong></p>
              <p><code>Route path="/old-path" element=Navigate to="/new-path" replace /</code></p>
            </div>
          </div>
        </div>

        <div className="theory-item">
          <h3>🧭 Navigation Programmatic với useNavigate</h3>
          <div className="navigate-explanation">
            <p><code>useNavigate</code> hook cho phép điều hướng programmatically trong React Router v6.</p>

            <h4>Cách sử dụng:</h4>
            <div className="code-example">
              <p><strong>Import và sử dụng useNavigate:</strong></p>
              <p><code>import useNavigate from 'react-router-dom';</code></p>
              <br />
              <p><code>function MyComponent() </code></p>
              <p><code>  const navigate = useNavigate();</code></p>
              <br />
              <p><code>  const handleClick = () =&gt; </code></p>
              <p><code>    navigate('/about'); // Điều hướng đến /about</code></p>
              <p><code>    navigate(-1); // Quay lại trang trước</code></p>
              <p><code>    navigate('/user/123', replace: true ); // Thay thế history</code></p>
              <p><code>  ;</code></p>
              <p><code></code></p>
            </div>

            <div className="demo-section">
              <h4>Demo useNavigate:</h4>
              <button onClick={handleNavigateToAbout} className="navigate-btn">
                Điều hướng đến trang About bằng useNavigate
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Home;
