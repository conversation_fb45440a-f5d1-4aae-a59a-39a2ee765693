/* About.css */
.about-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.about-container h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.profile-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
  position: relative;
}

.profile-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.profile-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
}

.profile-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid #ecf0f1;
}

.avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: white;
  font-size: 2rem;
  font-weight: bold;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  transition: transform 0.3s ease;
}

.avatar:hover {
  transform: scale(1.1);
}

.profile-header h2 {
  color: #2c3e50;
  margin: 0;
  font-size: 2rem;
}

.profile-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
  transition: all 0.3s ease;
  border-left: 4px solid #3498db;
}

.info-item:hover {
  background: #e9ecef;
  transform: translateX(5px);
}

.label {
  font-weight: 600;
  color: #2c3e50;
  min-width: 150px;
  margin-right: 1rem;
}

.value {
  color: #34495e;
  flex: 1;
}

.value a {
  color: #3498db;
  text-decoration: none;
  transition: color 0.3s ease;
}

.value a:hover {
  color: #2980b9;
  text-decoration: underline;
}

.profile-footer {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 2px solid #ecf0f1;
}

.profile-footer p {
  margin-bottom: 0.5rem;
  color: #7f8c8d;
  font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
  .about-container {
    padding: 15px;
  }
  
  .about-container h1 {
    font-size: 2rem;
  }
  
  .profile-card {
    padding: 1.5rem;
  }
  
  .avatar {
    width: 80px;
    height: 80px;
    font-size: 1.5rem;
  }
  
  .profile-header h2 {
    font-size: 1.5rem;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }
  
  .label {
    min-width: auto;
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
}
