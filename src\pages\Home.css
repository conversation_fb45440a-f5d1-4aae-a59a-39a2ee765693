/* Home.css */
.home-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.home-container h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.theory-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.theory-section h2 {
  text-align: center;
  color: #34495e;
  margin-bottom: 2rem;
  font-size: 2rem;
}

.theory-item {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.theory-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.theory-item h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  border-bottom: 2px solid #3498db;
  padding-bottom: 0.5rem;
}

.comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1rem;
}

.version-block {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #3498db;
}

.version-block h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.version-block ul {
  list-style: none;
  padding: 0;
}

.version-block li {
  margin-bottom: 0.8rem;
  padding-left: 1.5rem;
  position: relative;
}

.version-block li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #27ae60;
  font-weight: bold;
}

.concept-explanation h4 {
  color: #2c3e50;
  margin: 1.5rem 0 0.8rem 0;
  font-size: 1.1rem;
}

.concept-explanation p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.navigate-explanation {
  margin-top: 1rem;
}

.demo-section {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin-top: 1rem;
  text-align: center;
}

.navigate-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.navigate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.code-example {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  font-family: 'Courier New', monospace;
}

.code-example p {
  margin: 0.2rem 0;
  line-height: 1.4;
}

.code-example code {
  background: none;
  color: inherit;
  padding: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .home-container {
    padding: 15px;
  }
  
  .home-container h1 {
    font-size: 2rem;
  }
  
  .comparison {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .theory-item {
    padding: 1.5rem;
  }
  
  .version-block {
    padding: 1rem;
  }
}
