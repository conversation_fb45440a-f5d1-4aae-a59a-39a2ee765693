import { Link, useLocation } from 'react-router-dom';
import './Navigation.css';

function Navigation() {
  const location = useLocation();

  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <nav className="navigation">
      <div className="nav-container">
        <div className="nav-brand">
          <Link to="/home">
            <span className="brand-icon">⚛️</span>
            React Router Demo
          </Link>
        </div>
        
        <ul className="nav-menu">
          <li className="nav-item">
            <Link 
              to="/home" 
              className={`nav-link ${isActive('/home') ? 'active' : ''}`}
            >
              🏠 Trang chủ
            </Link>
          </li>
          <li className="nav-item">
            <Link 
              to="/about" 
              className={`nav-link ${isActive('/about') ? 'active' : ''}`}
            >
              👨‍🎓 Về tôi
            </Link>
          </li>
          <li className="nav-item">
            <Link 
              to="/contact" 
              className={`nav-link ${isActive('/contact') ? 'active' : ''}`}
            >
              📞 Liên hệ
            </Link>
          </li>
        </ul>
      </div>
    </nav>
  );
}

export default Navigation;
