import { Link, useNavigate } from 'react-router-dom';
import './NotFound.css';

function NotFound() {
  const navigate = useNavigate();

  const goBack = () => {
    navigate(-1);
  };

  const goHome = () => {
    navigate('/home');
  };

  return (
    <div className="notfound-container">
      <div className="notfound-content">
        <div className="error-code">404</div>
        <h1>Oops! Trang không tìm thấy</h1>
        <p className="error-message">
          Trang bạn đang tìm kiếm không tồn tại hoặc đã bị di chuyển.
        </p>
        
        <div className="notfound-actions">
          <button onClick={goHome} className="btn-primary">
            🏠 Về trang chủ
          </button>
          <button onClick={goBack} className="btn-secondary">
            ⬅️ Quay lại
          </button>
        </div>
        
        <div className="helpful-links">
          <h3><PERSON><PERSON><PERSON> trang có sẵn:</h3>
          <ul>
            <li><Link to="/home">🏠 Trang chủ</Link></li>
            <li><Link to="/about">👨‍🎓 Thông tin về tôi</Link></li>
            <li><Link to="/contact">📞 Liên hệ</Link></li>
          </ul>
        </div>
        
        <div className="error-illustration">
          <div className="floating-elements">
            <span>🔍</span>
            <span>❓</span>
            <span>🤔</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default NotFound;
