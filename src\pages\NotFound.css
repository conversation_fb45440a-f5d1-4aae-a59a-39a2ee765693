/* NotFound.css */
.notfound-container {
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.notfound-content {
  text-align: center;
  max-width: 600px;
  background: white;
  padding: 3rem;
  border-radius: 20px;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.notfound-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.error-code {
  font-size: 8rem;
  font-weight: bold;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  text-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.notfound-content h1 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 2rem;
}

.error-message {
  color: #7f8c8d;
  font-size: 1.2rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.notfound-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.notfound-actions button {
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
  box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
}

.btn-secondary:hover {
  background: #7f8c8d;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(127, 140, 141, 0.4);
}

.helpful-links {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 15px;
  margin-bottom: 2rem;
}

.helpful-links h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.helpful-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.helpful-links li {
  margin: 0;
}

.helpful-links a {
  color: #3498db;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: inline-block;
}

.helpful-links a:hover {
  background: #3498db;
  color: white;
  text-decoration: none;
  transform: translateX(5px);
}

.error-illustration {
  position: relative;
  height: 100px;
  margin-top: 2rem;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.floating-elements span {
  font-size: 2rem;
  animation: float 3s ease-in-out infinite;
  opacity: 0.7;
}

.floating-elements span:nth-child(1) {
  animation-delay: 0s;
}

.floating-elements span:nth-child(2) {
  animation-delay: 1s;
}

.floating-elements span:nth-child(3) {
  animation-delay: 2s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .notfound-content {
    padding: 2rem 1.5rem;
  }
  
  .error-code {
    font-size: 5rem;
  }
  
  .notfound-content h1 {
    font-size: 1.5rem;
  }
  
  .error-message {
    font-size: 1rem;
  }
  
  .notfound-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .notfound-actions button {
    width: 100%;
    max-width: 250px;
  }
  
  .helpful-links ul {
    align-items: center;
  }
}
